import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

const Hero = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <section className="relative min-h-screen pt-20 pb-16 overflow-hidden bg-gradient-to-br from-gray-50 via-white to-blue-50/30">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-[#355764]/10 to-[#5A8F7B]/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-[#5A8F7B]/10 to-[#355764]/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        
        {/* Floating Particles */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-[#355764] to-[#5A8F7B] rounded-full opacity-20 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          ></div>
        ))}
      </div>

      {/* Interactive Cursor Effect */}
      <div 
        className="absolute pointer-events-none z-10 w-96 h-96 bg-gradient-to-r from-[#355764]/5 to-[#5A8F7B]/5 rounded-full blur-3xl transition-all duration-300"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
        }}
      ></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          <div className={`space-y-8 transform transition-all duration-1000 ${
            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
          }`}>
            {/* AI Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#355764]/10 to-[#5A8F7B]/10 backdrop-blur-xl rounded-full border border-[#355764]/20 group hover:scale-105 transition-all duration-300">
              <Brain className="w-4 h-4 text-[#355764] mr-2 animate-pulse" />
              <span className="text-sm font-semibold bg-gradient-to-r from-[#355764] to-[#5A8F7B] bg-clip-text text-transparent">
                AI-Powered Website Intelligence
              </span>
              <Sparkles className="w-4 h-4 text-[#5A8F7B] ml-2 animate-spin" />
            </div>

            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                We Don't Just Build Websites — We Build{' '}
                <span className="relative">
                  <span className="bg-gradient-to-r from-[#355764] to-[#5A8F7B] bg-clip-text text-transparent">
                    AI-Powered Client Acquisition Systems
                  </span>
                  <div className="absolute -inset-1 bg-gradient-to-r from-[#355764]/20 to-[#5A8F7B]/20 blur-lg -z-10 animate-pulse"></div>
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 leading-relaxed">
                At Ocliq, we harness cutting-edge AI to create conversion-driven websites & automate your lead generation — 
                so you focus on what you do best while our intelligent systems work 24/7.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button className="group relative bg-gradient-to-r from-[#5A8F7B] to-[#355764] text-white px-8 py-4 rounded-xl hover:shadow-2xl hover:shadow-[#5A8F7B]/25 transition-all duration-500 font-semibold text-lg overflow-hidden">
                <span className="relative z-10 flex items-center justify-center">
                  Get Your Free AI Mockup
                  <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  <Zap size={16} className="ml-1 animate-pulse" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-[#355764] to-[#5A8F7B] opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -inset-1 bg-gradient-to-r from-[#5A8F7B] to-[#355764] rounded-xl blur opacity-30 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
              </button>
              
              <button className="group relative border-2 border-[#355764] text-[#355764] px-8 py-4 rounded-xl hover:bg-[#355764] hover:text-white transition-all duration-500 font-semibold text-lg overflow-hidden backdrop-blur-xl">
                <span className="relative z-10 flex items-center justify-center">
                  View Our AI Portfolio
                  <Sparkles size={16} className="ml-2 group-hover:animate-spin" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-[#355764]/5 to-[#5A8F7B]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </button>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <CheckCircle size={16} className="text-[#5A8F7B] animate-pulse" />
                <span className="font-medium">Trusted by 100+ AI-forward professionals</span>
              </div>
              <div className="flex -space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="w-8 h-8 bg-gradient-to-r from-[#355764] to-[#5A8F7B] rounded-full border-2 border-white animate-bounce" style={{ animationDelay: `${i * 200}ms` }}></div>
                ))}
              </div>
            </div>
          </div>

          <div className={`relative lg:ml-8 transform transition-all duration-1000 delay-300 ${
            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
          }`}>
            <div className="relative z-10 group">
              <div className="relative overflow-hidden rounded-2xl">
                <img 
                  src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800" 
                  alt="AI-powered digital workspace"
                  className="rounded-2xl shadow-2xl group-hover:scale-105 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-tr from-[#355764]/20 to-[#5A8F7B]/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                {/* Floating AI Elements */}
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-xl rounded-xl p-3 shadow-lg animate-float">
                  <Brain className="w-6 h-6 text-[#355764]" />
                </div>
                <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-xl rounded-xl p-3 shadow-lg animate-float" style={{ animationDelay: '1s' }}>
                  <Zap className="w-6 h-6 text-[#5A8F7B]" />
                </div>
              </div>
            </div>
            
            {/* Decorative Elements */}
            <div className="absolute inset-0 bg-gradient-to-tr from-[#355764]/20 to-[#5A8F7B]/20 rounded-2xl transform rotate-3 -z-10 group-hover:rotate-6 transition-transform duration-700"></div>
            <div className="absolute -inset-4 bg-gradient-to-tr from-[#355764]/10 to-[#5A8F7B]/10 rounded-2xl transform -rotate-3 -z-20 group-hover:-rotate-6 transition-transform duration-700"></div>
            
            {/* Glowing Orbs */}
            <div className="absolute -top-8 -right-8 w-16 h-16 bg-gradient-to-r from-[#5A8F7B] to-[#355764] rounded-full blur-xl opacity-50 animate-pulse"></div>
            <div className="absolute -bottom-8 -left-8 w-20 h-20 bg-gradient-to-r from-[#355764] to-[#5A8F7B] rounded-full blur-xl opacity-30 animate-pulse" style={{ animationDelay: '1s' }}></div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-[#355764]/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gradient-to-b from-[#355764] to-[#5A8F7B] rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;