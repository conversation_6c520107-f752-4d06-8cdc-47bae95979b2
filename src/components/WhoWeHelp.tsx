import React, { useState, useEffect } from 'react';
import { Scale, Users, Heart, Building, MapPin, Sparkles } from 'lucide-react';

const WhoWeHelp = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById('who-we-help');
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, []);

  const industries = [
    { 
      icon: Scale, 
      name: 'Lawyers', 
      description: 'Legal professionals',
      color: 'from-blue-500 to-indigo-600',
      stats: '+300% leads'
    },
    { 
      icon: Users, 
      name: 'Coaches', 
      description: 'Life & business coaches',
      color: 'from-purple-500 to-pink-600',
      stats: '+250% revenue'
    },
    { 
      icon: Heart, 
      name: 'Dentists', 
      description: 'Healthcare providers',
      color: 'from-red-500 to-rose-600',
      stats: '62% conversion'
    },
    { 
      icon: Building, 
      name: 'Agencies', 
      description: 'Digital agencies',
      color: 'from-green-500 to-emerald-600',
      stats: '+400% growth'
    },
    { 
      icon: MapPin, 
      name: 'Local Businesses', 
      description: 'Service providers',
      color: 'from-orange-500 to-amber-600',
      stats: '+180% calls'
    }
  ];

  return (
    <section id="who-we-help" className="py-20 bg-gradient-to-b from-white to-gray-50/50 relative overflow-hidden">
      {/* Background Animation */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-gradient-to-r from-[#355764]/5 to-[#5A8F7B]/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-gradient-to-r from-[#5A8F7B]/5 to-[#355764]/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className={`mb-16 transform transition-all duration-1000 ${
          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
        }`}>
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#355764]/10 to-[#5A8F7B]/10 backdrop-blur-xl rounded-full border border-[#355764]/20 mb-6">
            <Sparkles className="w-4 h-4 text-[#355764] mr-2 animate-pulse" />
            <span className="text-sm font-semibold bg-gradient-to-r from-[#355764] to-[#5A8F7B] bg-clip-text text-transparent">
              AI-Powered Solutions for Every Industry
            </span>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Who We Help <span className="bg-gradient-to-r from-[#355764] to-[#5A8F7B] bg-clip-text text-transparent">Dominate</span> Online
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Whether you're a solo consultant or a 20-person law firm, our AI-driven approach 
            tailors your online presence to turn visitors into high-value leads.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 mb-16">
          {industries.map((industry, index) => (
            <div 
              key={index}
              className={`group relative p-6 rounded-2xl transition-all duration-500 cursor-pointer transform ${
                isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
              } ${hoveredIndex === index ? 'scale-110 -translate-y-2' : 'hover:scale-105'}`}
              style={{ 
                animationDelay: `${index * 200}ms`,
                transitionDelay: `${index * 100}ms`
              }}
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              {/* Background Glow */}
              <div className={`absolute inset-0 bg-gradient-to-br ${industry.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-all duration-500 blur-xl`}></div>
              
              {/* Card Background */}
              <div className="absolute inset-0 bg-white/80 backdrop-blur-xl rounded-2xl border border-gray-200/50 group-hover:border-[#5A8F7B]/30 transition-all duration-500 group-hover:shadow-2xl group-hover:shadow-[#5A8F7B]/10"></div>
              
              <div className="relative z-10">
                <div className={`w-16 h-16 bg-gradient-to-br from-[#355764] to-[#5A8F7B] rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-2xl group-hover:shadow-[#5A8F7B]/25 transition-all duration-500 ${
                  hoveredIndex === index ? 'animate-pulse' : ''
                }`}>
                  <industry.icon size={32} className="text-white" />
                </div>
                
                <h3 className="font-bold text-gray-900 mb-2 group-hover:text-[#355764] transition-colors duration-300">
                  {industry.name}
                </h3>
                <p className="text-sm text-gray-600 mb-3">{industry.description}</p>
                
                {/* Stats Badge */}
                <div className={`inline-flex items-center px-3 py-1 bg-gradient-to-r ${industry.color} text-white text-xs font-semibold rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-2 group-hover:translate-y-0`}>
                  {industry.stats}
                </div>
              </div>

              {/* Floating Particles */}
              {hoveredIndex === index && (
                <>
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-1 h-1 bg-gradient-to-r from-[#355764] to-[#5A8F7B] rounded-full animate-float opacity-60"
                      style={{
                        left: `${20 + Math.random() * 60}%`,
                        top: `${20 + Math.random() * 60}%`,
                        animationDelay: `${i * 500}ms`,
                        animationDuration: '3s'
                      }}
                    ></div>
                  ))}
                </>
              )}
            </div>
          ))}
        </div>

        <div className={`transform transition-all duration-1000 delay-500 ${
          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
        }`}>
          <div className="relative p-8 bg-gradient-to-r from-gray-50 to-white rounded-3xl border border-gray-200/50 backdrop-blur-xl overflow-hidden group hover:shadow-2xl hover:shadow-[#5A8F7B]/10 transition-all duration-500">
            {/* Background Animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#355764]/5 to-[#5A8F7B]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            
            <div className="relative z-10">
              <div className="flex items-center justify-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-[#355764] to-[#5A8F7B] rounded-full flex items-center justify-center mr-4">
                  <Sparkles className="w-6 h-6 text-white animate-spin" />
                </div>
                <div className="text-left">
                  <p className="text-lg text-gray-700 font-medium">
                    "We've worked with professionals across industries to increase their revenue, 
                    authority, and peace of mind through strategic AI-powered web design."
                  </p>
                  <p className="text-sm text-[#355764] font-semibold mt-2">
                    — Ocliq AI Development Team
                  </p>
                </div>
              </div>
            </div>

            {/* Floating Elements */}
            <div className="absolute top-4 right-4 w-2 h-2 bg-[#5A8F7B] rounded-full animate-ping"></div>
            <div className="absolute bottom-4 left-4 w-2 h-2 bg-[#355764] rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhoWeHelp;