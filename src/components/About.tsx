import React from 'react';
import { Award, Users, Target, Clock } from 'lucide-react';

const About = () => {
  const values = [
    {
      icon: Target,
      title: 'Results-Driven',
      description: 'Every design decision is backed by conversion data and proven psychology'
    },
    {
      icon: Users,
      title: 'Client-Focused',
      description: 'We become an extension of your team, invested in your long-term success'
    },
    {
      icon: Award,
      title: 'Industry Expertise',
      description: 'Deep knowledge of professional services and what converts in your market'
    },
    {
      icon: Clock,
      title: 'Fast Delivery',
      description: 'Most projects completed in 2-4 weeks, not months'
    }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="relative">
            <img 
              src="https://images.pexels.com/photos/3184611/pexels-photo-3184611.jpeg?auto=compress&cs=tinysrgb&w=800"
              alt="Digital growth architects at work"
              className="rounded-2xl shadow-2xl"
            />
            <div className="absolute -bottom-6 -right-6 bg-[#355764] text-white p-6 rounded-xl shadow-lg">
              <div className="text-2xl font-bold">100+</div>
              <div className="text-sm">Successful Projects</div>
            </div>
          </div>

          <div className="space-y-8">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                About Ocliq.com
              </h2>
              <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
                <p>
                  We're not just designers — we're <strong className="text-[#355764]">digital growth architects</strong>. 
                  We've helped 100+ professionals increase their revenue, authority, and peace of mind through 
                  strategic web design and marketing automation.
                </p>
                <p>
                  Founded by industry veterans who understand the unique challenges of professional services, 
                  we combine cutting-edge design with proven conversion strategies to create websites that 
                  actually work for your business.
                </p>
                <p>
                  Our approach is different: we don't just build pretty websites. We build 
                  <strong className="text-[#355764]"> client acquisition systems</strong> that work 24/7 
                  to grow your practice while you focus on serving your clients.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              {values.map((value, index) => (
                <div key={index} className="space-y-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#355764] to-[#5A8F7B] rounded-lg flex items-center justify-center">
                    <value.icon size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">{value.title}</h3>
                    <p className="text-sm text-gray-600">{value.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="pt-6">
              <button className="bg-[#5A8F7B] text-white px-8 py-4 rounded-lg hover:bg-[#355764] transition-colors font-semibold text-lg">
                Let's Work Together
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;