import React from 'react';
import { Star, Quote } from 'lucide-react';

const Testimonials = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      title: 'Immigration Attorney',
      company: 'Chen Law Group',
      image: 'https://images.pexels.com/photos/3779448/pexels-photo-3779448.jpeg?auto=compress&cs=tinysrgb&w=300',
      quote: '<PERSON><PERSON><PERSON><PERSON> transformed our website from a digital brochure into a client acquisition machine. We went from 2-3 leads per month to 25+ qualified prospects.',
      result: '+10x Leads'
    },
    {
      name: '<PERSON>',
      title: 'Business Coach',
      company: 'Peak Performance Coaching',
      image: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=300',
      quote: 'The automation system they built has completely changed my business. I now have a consistent stream of high-quality leads coming in while I focus on coaching.',
      result: '+250% Revenue'
    },
    {
      name: 'Dr. <PERSON>',
      title: 'Dentist',
      company: 'Smile Dental Care',
      image: 'https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=300',
      quote: 'Our new website books appointments 24/7. The patient experience is seamless, and our conversion rate has never been higher. Worth every penny.',
      result: '62% Conversion'
    }
  ];

  const stats = [
    { number: '$1.2M+', label: 'Client Revenue Generated' },
    { number: '100+', label: 'Professionals Served' },
    { number: '300%', label: 'Average Lead Increase' },
    { number: '99%', label: 'Client Satisfaction' }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Real Results. Real Clients.
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our clients say about their transformation.
          </p>
        </div>

        {/* Stats Row */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-[#355764] mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-gray-100"
            >
              <div className="flex items-center mb-6">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={16} fill="currentColor" />
                  ))}
                </div>
                <span className="ml-3 text-sm font-medium text-[#355764] bg-[#355764]/10 px-3 py-1 rounded-full">
                  {testimonial.result}
                </span>
              </div>

              <div className="relative mb-6">
                <Quote size={24} className="text-[#5A8F7B] mb-4" />
                <p className="text-gray-700 leading-relaxed">
                  "{testimonial.quote}"
                </p>
              </div>

              <div className="flex items-center">
                <img 
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover mr-4"
                />
                <div>
                  <div className="font-semibold text-gray-900">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-gray-600">
                    {testimonial.title}, {testimonial.company}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <p className="text-lg text-gray-600 mb-6">
            Over $1.2M+ generated in client revenue
          </p>
          <button className="bg-[#355764] text-white px-8 py-4 rounded-lg hover:bg-[#5A8F7B] transition-colors font-semibold text-lg">
            Let's Talk About Your Growth
          </button>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;