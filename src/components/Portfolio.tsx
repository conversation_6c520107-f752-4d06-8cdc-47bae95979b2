import React from 'react';
import { ArrowUpRight, TrendingUp } from 'lucide-react';

const Portfolio = () => {
  const projects = [
    {
      title: 'Attorney Website',
      category: 'Legal Services',
      result: '+3x Leads',
      metric: '300% increase in qualified leads',
      image: 'https://images.pexels.com/photos/5668858/pexels-photo-5668858.jpeg?auto=compress&cs=tinysrgb&w=600',
      beforeAfter: 'Transformed a static brochure site into a lead-generating machine'
    },
    {
      title: 'Dental Landing Page',
      category: 'Healthcare',
      result: '62% Conversion',
      metric: 'Achieved 62% conversion rate',
      image: 'https://images.pexels.com/photos/6129507/pexels-photo-6129507.jpeg?auto=compress&cs=tinysrgb&w=600',
      beforeAfter: 'Redesigned patient acquisition funnel with automated booking'
    },
    {
      title: 'Business Coach Site',
      category: 'Coaching',
      result: '+250% Revenue',
      metric: 'Client revenue increased by 250%',
      image: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=600',
      beforeAfter: 'Built comprehensive coaching platform with client portal'
    },
    {
      title: 'Local Service Provider',
      category: 'Home Services',
      result: '+180% Calls',
      metric: '180% increase in phone inquiries',
      image: 'https://images.pexels.com/photos/4246120/pexels-photo-4246120.jpeg?auto=compress&cs=tinysrgb&w=600',
      beforeAfter: 'Optimized for local SEO and mobile conversions'
    },
    {
      title: 'Consulting Firm',
      category: 'Professional Services',
      result: '+400% Leads',
      metric: '400% increase in qualified prospects',
      image: 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=600',
      beforeAfter: 'Created authority-building content hub with lead magnets'
    },
    {
      title: 'Medical Practice',
      category: 'Healthcare',
      result: '45% Conv Rate',
      metric: '45% appointment booking rate',
      image: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg?auto=compress&cs=tinysrgb&w=600',
      beforeAfter: 'Streamlined patient onboarding with automated workflows'
    }
  ];

  return (
    <section id="portfolio" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Featured Projects & Results
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Real websites, real results. See how we've helped professionals like you 
            transform their online presence into revenue-generating assets.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <div 
              key={index}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-[#5A8F7B]/30"
            >
              <div className="relative overflow-hidden">
                <img 
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="absolute top-4 right-4 w-10 h-10 bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <ArrowUpRight size={20} className="text-[#355764]" />
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-[#5A8F7B] bg-[#5A8F7B]/10 px-3 py-1 rounded-full">
                    {project.category}
                  </span>
                  <div className="flex items-center text-[#355764] font-bold">
                    <TrendingUp size={16} className="mr-1" />
                    {project.result}
                  </div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {project.title}
                </h3>
                
                <p className="text-gray-600 mb-4">
                  {project.beforeAfter}
                </p>

                <div className="pt-4 border-t border-gray-100">
                  <p className="text-sm font-semibold text-[#355764]">
                    {project.metric}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <p className="text-lg text-gray-600 mb-6">
            Want to see more case studies and detailed results?
          </p>
          <button className="bg-[#5A8F7B] text-white px-8 py-4 rounded-lg hover:bg-[#355764] transition-colors font-semibold text-lg">
            View Complete Portfolio
          </button>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;