import React, { useState, useEffect } from 'react';
import { <PERSON>, Palette, Zap, <PERSON>, <PERSON>, <PERSON>rkles } from 'lucide-react';

const Process = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById('process');
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const steps = [
    {
      icon: Search,
      title: 'AI Audit',
      subtitle: 'Our AI identifies what\'s holding your site back',
      description: 'Advanced machine learning algorithms analyze your current website, identifying conversion barriers, user experience issues, and missed opportunities with surgical precision.',
      color: 'from-[#355764] to-[#4a6b78]',
      glowColor: 'shadow-[#355764]/25',
      features: ['Conversion Analysis', 'UX Heatmapping', 'Competitor Intelligence']
    },
    {
      icon: Palette,
      title: 'AI Design & Convert',
      subtitle: 'We create stunning, AI-optimized mockups',
      description: 'Our AI design system creates beautiful, conversion-optimized designs tailored to your brand and target audience, using data-driven design principles.',
      color: 'from-[#4a6b78] to-[#5A8F7B]',
      glowColor: 'shadow-[#4a6b78]/25',
      features: ['Smart Layouts', 'Brand Intelligence', 'Conversion Psychology']
    },
    {
      icon: Zap,
      title: 'AI Automation',
      subtitle: 'We plug in our intelligent lead-gen system',
      description: 'Deploy advanced AI-powered lead capture, automated nurturing sequences, and intelligent conversion optimization that learns and improves over time.',
      color: 'from-[#5A8F7B] to-[#6da085]',
      glowColor: 'shadow-[#5A8F7B]/25',
      features: ['Smart Chatbots', 'Predictive Analytics', 'Auto-Optimization']
    },
    {
      icon: Rocket,
      title: 'AI Launch & Scale',
      subtitle: 'You go live, our AI optimizes continuously',
      description: 'Website launch with continuous AI monitoring and optimization. Our systems automatically test, learn, and improve your conversion rates 24/7.',
      color: 'from-[#6da085] to-[#5A8F7B]',
      glowColor: 'shadow-[#6da085]/25',
      features: ['Auto A/B Testing', 'Performance Monitoring', 'Continuous Learning']
    }
  ];

  return (
    <section id="process" className="py-20 bg-gradient-to-b from-gray-50/50 to-white relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-0 w-96 h-96 bg-gradient-to-r from-[#355764]/10 to-[#5A8F7B]/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-gradient-to-r from-[#5A8F7B]/10 to-[#355764]/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        
        {/* Neural Network Lines */}
        <svg className="absolute inset-0 w-full h-full opacity-5" viewBox="0 0 1000 1000">
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#355764" />
              <stop offset="100%" stopColor="#5A8F7B" />
            </linearGradient>
          </defs>
          {[...Array(20)].map((_, i) => (
            <line
              key={i}
              x1={Math.random() * 1000}
              y1={Math.random() * 1000}
              x2={Math.random() * 1000}
              y2={Math.random() * 1000}
              stroke="url(#lineGradient)"
              strokeWidth="1"
              className="animate-pulse"
              style={{ animationDelay: `${i * 200}ms` }}
            />
          ))}
        </svg>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`text-center mb-16 transform transition-all duration-1000 ${
          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
        }`}>
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#355764]/10 to-[#5A8F7B]/10 backdrop-blur-xl rounded-full border border-[#355764]/20 mb-6">
            <Brain className="w-4 h-4 text-[#355764] mr-2 animate-pulse" />
            <span className="text-sm font-semibold bg-gradient-to-r from-[#355764] to-[#5A8F7B] bg-clip-text text-transparent">
              AI-Powered Methodology
            </span>
            <Sparkles className="w-4 h-4 text-[#5A8F7B] ml-2 animate-spin" />
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Our <span className="bg-gradient-to-r from-[#355764] to-[#5A8F7B] bg-clip-text text-transparent">AI-Driven</span> Process
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A revolutionary 4-step AI methodology that transforms your website into an intelligent 
            client acquisition machine that learns and optimizes itself.
          </p>
        </div>

        {/* Process Steps */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {steps.map((step, index) => (
            <div 
              key={index}
              className={`relative group cursor-pointer transform transition-all duration-700 ${
                isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
              } ${activeStep === index ? 'scale-105' : 'hover:scale-105'}`}
              style={{ animationDelay: `${index * 200}ms` }}
              onMouseEnter={() => setActiveStep(index)}
            >
              {/* Card Background */}
              <div className={`bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-lg hover:shadow-2xl ${
                activeStep === index ? `hover:${step.glowColor}` : ''
              } transition-all duration-500 h-full border border-gray-100 hover:border-[#5A8F7B]/30 relative overflow-hidden`}>
                
                {/* Active Indicator */}
                {activeStep === index && (
                  <div className="absolute inset-0 bg-gradient-to-br from-[#355764]/5 to-[#5A8F7B]/5 rounded-3xl"></div>
                )}

                <div className="relative z-10">
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-br ${step.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-500 ${
                    activeStep === index ? 'animate-pulse shadow-2xl' : ''
                  }`}>
                    <step.icon size={28} className="text-white" />
                  </div>
                  
                  {/* Content */}
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center mb-2">
                        <span className={`w-8 h-8 bg-gradient-to-r ${step.color} text-white rounded-full flex items-center justify-center text-sm font-bold mr-3`}>
                          {index + 1}
                        </span>
                        <h3 className="text-xl font-bold text-gray-900">
                          {step.title}
                        </h3>
                      </div>
                      <p className="text-[#355764] font-semibold text-sm mb-3">
                        {step.subtitle}
                      </p>
                    </div>
                    
                    <p className="text-gray-600 leading-relaxed text-sm">
                      {step.description}
                    </p>

                    {/* Features */}
                    <div className="space-y-2">
                      {step.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center text-xs text-gray-500">
                          <div className="w-1.5 h-1.5 bg-gradient-to-r from-[#355764] to-[#5A8F7B] rounded-full mr-2"></div>
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Connection Line */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-[#355764] to-[#5A8F7B] transform -translate-y-1/2 z-20">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#355764] to-[#5A8F7B] animate-pulse"></div>
                  </div>
                )}

                {/* Floating Particles */}
                {activeStep === index && (
                  <>
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-1 h-1 bg-gradient-to-r from-[#355764] to-[#5A8F7B] rounded-full animate-float opacity-60"
                        style={{
                          left: `${10 + Math.random() * 80}%`,
                          top: `${10 + Math.random() * 80}%`,
                          animationDelay: `${i * 300}ms`,
                          animationDuration: '4s'
                        }}
                      ></div>
                    ))}
                  </>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className={`text-center transform transition-all duration-1000 delay-500 ${
          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
        }`}>
          <div className="relative inline-block">
            <button className="relative bg-gradient-to-r from-[#355764] to-[#5A8F7B] text-white px-8 py-4 rounded-xl hover:shadow-2xl hover:shadow-[#5A8F7B]/25 transition-all duration-500 font-semibold text-lg group overflow-hidden">
              <span className="relative z-10 flex items-center">
                Start Your AI Transformation Today
                <Brain className="ml-2 w-5 h-5 animate-pulse" />
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-[#5A8F7B] to-[#355764] opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -inset-1 bg-gradient-to-r from-[#355764] to-[#5A8F7B] rounded-xl blur opacity-30 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Process;