import React, { useState, useEffect } from 'react';
import { Menu, X, Sparkles } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className={`fixed w-full z-50 transition-all duration-500 ${
      scrolled
        ? 'bg-background-light/80 backdrop-blur-xl shadow-lg border-b border-white/20'
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center group">
            <div className="relative">
              <Sparkles className="w-8 h-8 text-secondary mr-3 animate-pulse" />
              <div className="absolute inset-0 w-8 h-8 bg-secondary/20 rounded-full animate-ping"></div>
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Ocliq
            </h1>
            <span className="ml-2 px-2 py-1 text-xs font-semibold bg-gradient-to-r from-secondary to-primary text-white rounded-full animate-bounce">
              AI
            </span>
          </div>
          
          <nav className="hidden md:flex space-x-8">
            {['Services', 'Process', 'Portfolio', 'About', 'Contact'].map((item, index) => (
              <a
                key={item}
                href={`#${item.toLowerCase()}`}
                className="relative text-text-secondary hover:text-primary transition-all duration-300 group"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                {item}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary transition-all duration-300 group-hover:w-full"></span>
              </a>
            ))}
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            <a
              href="#portfolio"
              className="relative text-primary hover:text-secondary transition-all duration-300 font-medium group"
            >
              View Our Work
              <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 -z-10"></div>
            </a>
            <button className="relative bg-gradient-to-r from-secondary to-primary text-white px-6 py-2 rounded-xl hover:shadow-2xl hover:shadow-secondary/25 transition-all duration-300 font-medium group overflow-hidden">
              <span className="relative z-10">Get Free AI Mockup</span>
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-secondary opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute -inset-1 bg-gradient-to-r from-secondary to-primary rounded-xl blur opacity-30 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
            </button>
          </div>

          <button 
            className="md:hidden relative"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <div className="w-6 h-6 relative">
              <Menu className={`absolute inset-0 transition-all duration-300 ${isMenuOpen ? 'rotate-90 opacity-0' : 'rotate-0 opacity-100'}`} size={24} />
              <X className={`absolute inset-0 transition-all duration-300 ${isMenuOpen ? 'rotate-0 opacity-100' : '-rotate-90 opacity-0'}`} size={24} />
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`md:hidden overflow-hidden transition-all duration-500 ${
          isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className="py-4 border-t border-gray-200/50 backdrop-blur-xl">
            <div className="flex flex-col space-y-4">
              {['Services', 'Process', 'Portfolio', 'About', 'Contact'].map((item, index) => (
                <a
                  key={item}
                  href={`#${item.toLowerCase()}`}
                  className="text-text-secondary hover:text-primary transition-all duration-300 transform hover:translate-x-2"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  {item}
                </a>
              ))}
              <div className="pt-4 border-t border-gray-200/50">
                <button className="w-full bg-gradient-to-r from-secondary to-primary text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium">
                  Get Free AI Mockup
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;