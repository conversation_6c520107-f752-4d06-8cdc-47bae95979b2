import React from 'react';
import { Mail, Phone, MapPin, Linkedin, Twitter, Facebook } from 'lucide-react';

const Footer = () => {
  const services = [
    'Website Design & Development',
    'Conversion Optimization',
    'Lead Generation Automation',
    'Brand Development',
    'SEO & Digital Marketing'
  ];

  const industries = [
    'Legal Services',
    'Healthcare & Dental',
    'Coaching & Consulting',
    'Real Estate',
    'Home Services'
  ];

  return (
    <footer className="bg-[#355764] text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-1">
            <h3 className="text-2xl font-bold mb-6">Ocliq</h3>
            <p className="text-blue-100 mb-6 leading-relaxed">
              We build client acquisition systems for professionals who want to grow their practice 
              without the stress of constant marketing.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-[#5A8F7B] transition-colors">
                <Linkedin size={20} />
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-[#5A8F7B] transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-[#5A8F7B] transition-colors">
                <Facebook size={20} />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Services</h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <a href="#" className="text-blue-100 hover:text-white transition-colors">
                    {service}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Industries */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Industries</h4>
            <ul className="space-y-3">
              {industries.map((industry, index) => (
                <li key={index}>
                  <a href="#" className="text-blue-100 hover:text-white transition-colors">
                    {industry}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Get In Touch</h4>
            <div className="space-y-4">
              <div className="flex items-center">
                <Mail size={18} className="mr-3 text-[#5A8F7B]" />
                <a href="mailto:<EMAIL>" className="text-blue-100 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <Phone size={18} className="mr-3 text-[#5A8F7B]" />
                <a href="tel:+1234567890" className="text-blue-100 hover:text-white transition-colors">
                  (*************
                </a>
              </div>
              <div className="flex items-start">
                <MapPin size={18} className="mr-3 text-[#5A8F7B] mt-1" />
                <span className="text-blue-100">
                  Remote Team<br />
                  Serving Clients Worldwide
                </span>
              </div>
            </div>

            <div className="mt-8">
              <button className="bg-[#5A8F7B] text-white px-6 py-3 rounded-lg hover:bg-white hover:text-[#355764] transition-colors font-semibold">
                Start Your Project
              </button>
            </div>
          </div>
        </div>

        <div className="border-t border-white/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-blue-100 text-sm mb-4 md:mb-0">
              © 2024 Ocliq.com. All rights reserved.
            </div>
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-blue-100 hover:text-white transition-colors">Privacy Policy</a>
              <a href="#" className="text-blue-100 hover:text-white transition-colors">Terms of Service</a>
              <a href="#" className="text-blue-100 hover:text-white transition-colors">Cookies</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;