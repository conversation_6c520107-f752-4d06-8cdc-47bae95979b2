@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(1deg);
    }
    66% {
      transform: translateY(-5px) rotate(-1deg);
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(90, 143, 123, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(90, 143, 123, 0.6);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-slideInUp {
    animation: slideInUp 0.6s ease-out forwards;
  }

  .animate-fadeInScale {
    animation: fadeInScale 0.5s ease-out forwards;
  }

  .glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .gradient-text {
    background: linear-gradient(135deg, #355764, #5A8F7B);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .neural-bg {
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(53, 87, 100, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(90, 143, 123, 0.1) 0%, transparent 50%);
  }

  .ai-gradient {
    background: linear-gradient(135deg, 
      rgba(53, 87, 100, 0.1) 0%, 
      rgba(90, 143, 123, 0.1) 50%, 
      rgba(53, 87, 100, 0.1) 100%);
  }

  .pulse-glow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    box-shadow: 0 0 0 0 rgba(90, 143, 123, 0.7);
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(90, 143, 123, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(90, 143, 123, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(90, 143, 123, 0);
    }
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #355764, #5A8F7B);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5A8F7B, #355764);
}

/* Selection color */
::selection {
  background: rgba(90, 143, 123, 0.3);
  color: #355764;
}

/* Focus styles */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid rgba(90, 143, 123, 0.5);
  outline-offset: 2px;
}

/* Loading animation */
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading {
  animation: loading 1s linear infinite;
}

/* Glassmorphism utilities */
.glass {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(53, 87, 100, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
}